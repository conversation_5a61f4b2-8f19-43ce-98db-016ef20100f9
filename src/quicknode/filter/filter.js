const ERC_TRANSFER_TOPIC = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';
function stripAddress(topic) {
  return '0x' + topic.slice(-40).toLowerCase();
}

function main(payload) {
  const targetContracts = new Set([
    '0xTokenAddr1'.toLowerCase(),
    '0xTokenAddr2'.toLowerCase(),
  ]);
  const watchedWallets = new Set([
    '0xWallet1'.toLowerCase(),
    '0xWallet2'.toLowerCase(),
  ]);

  const transfers = [];
  for (const txOrEvent of payload.data) {
    // Example: if log-based filtering available in Webhooks data
    const log = txOrEvent.log;
    if (
      log &&
      log.topics?.[0] === ERC_TRANSFER_TOPIC &&
      targetContracts.has(log.address.toLowerCase())
    ) {
      const from = stripAddress(log.topics[1]);
      const to = stripAddress(log.topics[2]);
      if (watchedWallets.has(from) || watchedWallets.has(to)) {
        transfers.push({
          txHash: log.transactionHash,
          contract: log.address,
          from,
          to,
          value: BigInt(log.data).toString(),
        });
      }
    }
  }
  return transfers.length ? { transfers } : null;
}
