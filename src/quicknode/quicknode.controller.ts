import { <PERSON>, Get, Post, Body, Patch, Param, Delete, Req } from '@nestjs/common';
import { QuicknodeService } from './quicknode.service';
import { CreateQuicknodeDto } from './dto/create-quicknode.dto';
import { UpdateQuicknodeDto } from './dto/update-quicknode.dto';
import { ApiExcludeEndpoint } from '@nestjs/swagger';

@Controller('quicknode')
export class QuicknodeController {
  constructor(private readonly quicknodeService: QuicknodeService) {}

  @Post('log')      
  // @ApiExcludeEndpoint()
  async qnWebhook(@Req() request: any) {
    console.log("triggered");
    return await this.quicknodeService.log(request);
  }

  @Post('history')
  async getHistory() { 
    return await this.quicknodeService.updateWebhook();
  }
}
