import { Injectable } from '@nestjs/common';
import { verifySignature } from 'common/helper/quicknode';
import { readFileSync } from 'fs';
import { join } from 'path';
import { HttpService } from '@nestjs/axios';
import { lastValueFrom } from 'rxjs';
import configuration from 'config/configuration';

@Injectable()
export class QuicknodeService {
  private readonly base = 'https://api.quicknode.com/webhooks/rest/v1/webhooks';
  constructor(private readonly http: HttpService) { }

  /** Builds JS filter string from DB-sourced contract and wallet lists */
  private async buildFilterJs(): Promise<string> {
    const contracts = [
      '******************************************',
      '******************************************',
    ];
    const wallets = [
      '******************************************',
      '******************************************',
    ];

    return `
    const ERC_TRANSFER_TOPIC = '0xddf252ad1be2c89b69c2b068fc378daa952ba7f163c4a11628f55a4df523b3ef';
    function stripAddress(topic){ return '0x' + topic.slice(-40).toLowerCase(); }
    function main(payload) {
      const targetContracts = new Set(${JSON.stringify(contracts.map((c) => c.toLowerCase()))});
      const watchedWallets = new Set(${JSON.stringify(wallets.map((w) => w.toLowerCase()))});
      const erc20Transfers = [];

      for (const receipt of payload.data.flatMap(b => b.receipts || [])) {
        if (!receipt.logs) continue;

        for (const log of receipt.logs) {
          const addr = log.address?.toLowerCase();
          if (
            log.topics?.[0] === ERC_TRANSFER_TOPIC &&
            targetContracts.has(addr)
          ) {
            const from = stripAddress(log.topics[1] || '');
            const to = stripAddress(log.topics[2] || '');
            const valueRaw = log.data || '0x0';

            // If wallet filters exist, apply them; else accept all
            if (
              watchedWallets.size === 0 ||
              watchedWallets.has(from) ||
              watchedWallets.has(to)
            ) {
              erc20Transfers.push({
                txHash: receipt.transactionHash,
                contract: addr,
                from,
                to,
                value: BigInt(valueRaw).toString(),
                rawValue: valueRaw,
                topics: log.topics
              });
            }

            // Break to avoid duplicate receipt entries (one transfer per receipt)
            break;
          }
        }
      }

      return erc20Transfers.length ? { transfers: erc20Transfers } : null;
    }
  `;
  }

  async log(request) {
    const nonce = request.headers['x-qn-nonce'];
    const timestamp = request.headers['x-qn-timestamp'];
    const givenSignature = request.headers['x-qn-signature'];

    if (!nonce || !timestamp || !givenSignature) {
      console.error('Missing required headers');
    }
    console.log(request.body);

    const payloadString = JSON.stringify(request.body);
    const isValid = await verifySignature(
      payloadString,
      nonce,
      timestamp,
      givenSignature,
    );

    console.log(isValid);

    //add to queue
  }

  async updateWebhook() {
    const webhookId = '154b8b01-dd95-4c53-99c1-1f0bbb6bd673';
    const destUrl = 'https://scanner.cake5x.com/quicknode/log';
    const filterJs = await this.buildFilterJs();
    const filterB64 = Buffer.from(filterJs).toString('base64');

    const body = {
      filter_function: filterB64,
      destination_attributes: {
        url: destUrl,
        security_token: configuration().qn_token,
        compression: 'gzip',
      },
    };
    const resp = await lastValueFrom(
      this.http.patch(`${this.base}/${webhookId}`, body, {
        headers: {
          'x-api-key': configuration().qn_api_key,
          'Content-Type': 'application/json',
        },
      }),
    );
    return resp.data;
  }
}
