import { Test, TestingModule } from '@nestjs/testing';
import { Quicknode2Service } from './quicknode-2.service';

describe('Quicknode2Service', () => {
  let service: Quicknode2Service;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      providers: [Quicknode2Service],
    }).compile();

    service = module.get<Quicknode2Service>(Quicknode2Service);
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });
});
