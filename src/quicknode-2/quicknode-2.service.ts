import { Process } from '@nestjs/bull';
import { HttpService } from '@nestjs/axios';
import { HttpException, HttpStatus, Injectable, Logger } from '@nestjs/common';
import { firstValueFrom, isObservable, Observable } from 'rxjs';

@Injectable()
export class Quicknode2Service {

  private readonly logger = new Logger(Quicknode2Service.name);
  private readonly baseUrl = 'https://api.quicknode.com/webhooks/rest/v1';
  private readonly apiKey = process.env.QN_API_KEY as string;

  constructor(
    private readonly HttpService: HttpService) {
    if (!this.apiKey) {
      throw new Error('QN_API_KEY is not defined');
    }
  }

  private getHeaders() {
    return {
      'x-api-key': this.apiKey,
      'Content-Type': 'application/json',
    };
  }

  protected async handleRequest<T>(
    request: Observable<T> | Promise<T>,
    operation: string
  ): Promise<T> {
    try {
      const response = isObservable(request) 
        ? await firstValueFrom(request)
        : await request;

      this.logger.log(`YYYIPPPEEE ${operation} successful`);
      return response.data;
    } catch (error) {
      const status = error.response?.status || HttpStatus.INTERNAL_SERVER_ERROR;
      const message = error.response?.data || `${operation} failed`;

      this.logger.error(`NEEIINN ${operation} failed:`, message);
      throw new HttpException(message, status);
    }
  }
}
