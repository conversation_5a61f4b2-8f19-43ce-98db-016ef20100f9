import { Test, TestingModule } from '@nestjs/testing';
import { Quicknode2Controller } from './quicknode-2.controller';
import { Quicknode2Service } from './quicknode-2.service';

describe('Quicknode2Controller', () => {
  let controller: Quicknode2Controller;

  beforeEach(async () => {
    const module: TestingModule = await Test.createTestingModule({
      controllers: [Quicknode2Controller],
      providers: [Quicknode2Service],
    }).compile();

    controller = module.get<Quicknode2Controller>(Quicknode2Controller);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });
});
