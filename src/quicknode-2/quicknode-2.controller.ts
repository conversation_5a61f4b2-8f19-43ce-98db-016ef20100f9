import { Controller, Get, Post, Body, Patch, Param, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { Quicknode2Service } from './quicknode-2.service';

@Controller('quicknode-2')
export class Quicknode2Controller {
  constructor(private readonly quicknode2Service: Quicknode2Service) {}

  // create webhook 
  @Post('create/webhook')
  async createWebhook() {
    console.log("Creating Quicknode 2 webhook");
    return await this.quicknode2Service.createWebhook();
  }

  // create webhook with template ( filters, contract , wallet )

  // get all webhooks

  // get webhook by id 

  // update existing webhook 

  // delete webhook by id 

  // activate webhook by id 

  //pause webhook by id 

  //test filter 

}
