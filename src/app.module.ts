import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { MoralisModule } from './moralis/moralis.module';
import { TypeOrmModule } from '@nestjs/typeorm';
import configuration from 'config/configuration';
import { <PERSON><PERSON><PERSON> } from './moralis/entities/morali.entity';
import { QueueModule } from './queue/queue.module';
import { QuicknodeModule } from './quicknode/quicknode.module';
import { Quicknode2Module } from './quicknode-2/quicknode-2.module';

@Module({
  imports: [
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: configuration().database.host,
      port: configuration().database.port,
      username: configuration().database.username,
      password: configuration().database.password,
      database: configuration().database.database,
      entities: [
        Morali,
      ],
      synchronize: configuration().database.synchronize === 'true' ? true : false,
    }),
    MoralisModule,
    QueueModule,
    QuicknodeModule,
    Quicknode2Module
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
